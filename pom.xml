<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.dromara</groupId>
    <artifactId>ruoyi-vue-plus</artifactId>
    <version>${revision}</version>

    <name>RuoYi-Vue-Plus</name>
    <url>https://gitee.com/dromara/RuoYi-Vue-Plus</url>
    <description>Dromara RuoYi-Vue-Plus多租户管理系统</description>

    <properties>
        <revision>5.4.0</revision>
        <spring-boot.version>3.4.6</spring-boot.version>
        <spring-ai.version>1.0.0</spring-ai.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <mybatis.version>3.5.16</mybatis.version>
        <springdoc.version>2.8.8</springdoc.version>
        <therapi-javadoc.version>0.15.0</therapi-javadoc.version>
        <fastexcel.version>1.2.0</fastexcel.version>
        <velocity.version>2.3</velocity.version>
        <satoken.version>1.42.0</satoken.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <hutool.version>5.8.35</hutool.version>
        <spring-boot-admin.version>3.4.7</spring-boot-admin.version>
        <redisson.version>3.45.1</redisson.version>
        <lock4j.version>2.2.7</lock4j.version>
        <dynamic-ds.version>4.3.1</dynamic-ds.version>
        <snailjob.version>1.5.0</snailjob.version>
        <mapstruct-plus.version>1.4.8</mapstruct-plus.version>
        <mapstruct-plus.lombok.version>0.2.0</mapstruct-plus.lombok.version>
        <lombok.version>1.18.36</lombok.version>
        <bouncycastle.version>1.80</bouncycastle.version>
        <justauth.version>1.16.7</justauth.version>
        <!-- 离线IP地址定位库 -->
        <ip2region.version>2.7.0</ip2region.version>

        <!-- OSS 配置 -->
        <aws.sdk.version>2.28.22</aws.sdk.version>
        <!-- SMS 配置 -->
        <sms4j.version>3.3.4</sms4j.version>
        <!-- 限制框架中的fastjson版本 -->
        <fastjson.version>1.2.83</fastjson.version>
        <!-- 面向运行时的D-ORM依赖 -->
        <anyline.version>8.7.2-20250101</anyline.version>
        <!-- 工作流配置 -->
        <warm-flow.version>1.7.3</warm-flow.version>

        <!--数据库文档生成-->
        <screw-core.ersion>1.0.5</screw-core.ersion>
        <liquibase-core.version>4.31.1</liquibase-core.version>

        <!-- 插件版本 -->
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <maven-war-plugin.version>3.2.2</maven-war-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>
        <!-- 打包默认跳过测试 -->
        <skipTests>true</skipTests>
    </properties>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>local</profiles.active>
                <logging.level>info</logging.level>
                <monitor.username>ruoyi</monitor.username>
                <monitor.password>123456</monitor.password>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <logging.level>info</logging.level>
                <monitor.username>ruoyi</monitor.username>
                <monitor.password>123456</monitor.password>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <logging.level>warn</logging.level>
                <monitor.username>ruoyi</monitor.username>
                <monitor.password>123456</monitor.password>
            </properties>
        </profile>
    </profiles>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- spring-ai -->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- Warm-Flow国产工作流引擎, 在线文档：http://warm-flow.cn/ -->
            <dependency>
                <groupId>org.dromara.warm</groupId>
                <artifactId>warm-flow-mybatis-plus-sb3-starter</artifactId>
                <version>${warm-flow.version}</version>
            </dependency>
            <dependency>
                <groupId>org.dromara.warm</groupId>
                <artifactId>warm-flow-plugin-ui-sb-web</artifactId>
                <version>${warm-flow.version}</version>
            </dependency>

            <!-- JustAuth 的依赖配置-->
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${justauth.version}</version>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-common-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.therapi</groupId>
                <artifactId>therapi-runtime-javadoc</artifactId>
                <version>${therapi-javadoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>${fastexcel.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${satoken.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- dynamic-datasource 多数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <!--  AWS SDK for Java 2.x  -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <!-- 基于 AWS CRT 的 S3 客户端的性能增强的 S3 传输管理器 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3-transfer-manager</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <!-- 将基于 Netty 的 HTTP 客户端从类路径中移除 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>netty-nio-client</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <!--短信sms4j-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!-- SnailJob Client -->
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-starter</artifactId>
                <version>${snailjob.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-job-core</artifactId>
                <version>${snailjob.version}</version>
            </dependency>

            <!-- 加密包引入 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.linpeilie</groupId>
                <artifactId>mapstruct-plus-spring-boot-starter</artifactId>
                <version>${mapstruct-plus.version}</version>
            </dependency>

            <!-- 离线IP地址定位库 ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- 数据库文档 -->
            <dependency>
                <groupId>cn.smallbun.screw</groupId>
                <artifactId>screw-core</artifactId>
                <version>${screw-core.ersion}</version>
                <exclusions>
                    <!-- 移除 Freemarker 依赖，采用 Velocity 作为模板引擎 -->
                    <exclusion>
                        <groupId>org.freemarker</groupId>
                        <artifactId>freemarker</artifactId>
                    </exclusion>
                    <!-- 最新版screw-core1.0.5依赖fastjson1.2.73存在漏洞，移除。 -->
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-core</artifactId>
                <version>${liquibase-core.version}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-system</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-generator</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-demo</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--  工作流模块  -->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-workflow</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- AI模块-->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-syeran</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库模块-->
            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-database</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-chat-api</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.dromara</groupId>
                <artifactId>ruoyi-knowledge-api</artifactId>
                <version>${revision}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>ruoyi-admin</module>
        <module>ruoyi-common</module>
        <module>ruoyi-extend</module>
        <module>ruoyi-modules</module>
        <module>ruoyi-modules-api</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>${therapi-javadoc.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                        <path>
                            <groupId>io.github.linpeilie</groupId>
                            <artifactId>mapstruct-plus-processor</artifactId>
                            <version>${mapstruct-plus.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${mapstruct-plus.lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- 单元测试使用 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <!-- 根据打包环境执行对应的@Tag测试方法 -->
                    <groups>${profiles.active}</groups>
                    <!-- 排除标签 -->
                    <excludedGroups>exclude</excludedGroups>
                </configuration>
            </plugin>
            <!-- 统一版本号管理 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>banner*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>huawei nexus</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>huawei nexus</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>


