package org.dromara.db.controller;


import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.smallbun.screw.core.engine.EngineFileType;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.db.utils.DataBaseDocUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据库文档Controller
 *
 * <AUTHOR> wrote on 2022/8/28.
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/doc")
public class DatabaseDocController {

    /**
     * 导出HTML数据库文档
     * @param deleteFile 是否删除临时文件（true-是，false-否）
     * @param response 响应体
     */
    @GetMapping("/exportHtml")
    @Log(title = "数据库文档", businessType = BusinessType.EXPORT)
    @SaCheckPermission("database:doc:export")
    public void exportHtml(@RequestParam(defaultValue = "true") Boolean deleteFile, HttpServletResponse response) {
        DataBaseDocUtil.doExportFile(EngineFileType.HTML, deleteFile, response);
    }

    /**
     * 导出WORD数据库文档
     * @param deleteFile 是否删除临时文件（true-是，false-否）
     * @param response 响应体
     */
    @GetMapping("/exportWord")
    @Log(title = "数据库文档", businessType = BusinessType.EXPORT)
    @SaCheckPermission("database:doc:export")
    public void exportWord(@RequestParam(defaultValue = "true") Boolean deleteFile, HttpServletResponse response) {
        DataBaseDocUtil.doExportFile(EngineFileType.WORD, deleteFile, response);
    }

    /**
     * 导出Markdown数据库文档
     * @param deleteFile 是否删除临时文件（true-是，false-否）
     * @param response 响应体
     */
    @GetMapping("/exportMarkdown")
    @Log(title = "数据库文档", businessType = BusinessType.EXPORT)
    @SaCheckPermission("database:doc:export")
    public void exportMarkdown(@RequestParam(defaultValue = "true") Boolean deleteFile,
                               HttpServletResponse response) {
        DataBaseDocUtil.doExportFile(EngineFileType.MD, deleteFile, response);
    }

}
