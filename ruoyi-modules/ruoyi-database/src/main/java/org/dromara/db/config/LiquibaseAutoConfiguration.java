package org.dromara.db.config;

import liquibase.integration.spring.SpringLiquibase;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

@Configuration
@EnableConfigurationProperties({DataSourceProperties.class, LiquibaseProperties.class})
@ConditionalOnProperty(prefix = "spring.liquibase", value = {"enabled"}, havingValue = "true")
public class LiquibaseAutoConfiguration {
//    @Bean
//    public SpringLiquibase liquibase(DataSource dataSource, LiquibaseProperties liquibaseProperties) {
//        SpringLiquibase liquibase = new SpringLiquibase();
//        liquibase.setDataSource(dataSource);
//        //制定changelog的位置，这里使用的一个master文件引用其他文件的方式
//        liquibase.setChangeLog(liquibaseProperties.getChangeLog());
//        liquibase.setContexts(StringUtils.join(",", liquibaseProperties.getContexts()));
//        liquibase.setDefaultSchema(liquibaseProperties.getDefaultSchema());
//        liquibase.setDropFirst(liquibaseProperties.isDropFirst());
//        liquibase.setShouldRun(true);
//        return liquibase;
//    }

    @Value("${update.module:}")
    private String module;

    @Value("${liquibase.schema:}")
    private String liquibaseSchema;

    @Bean
    public SpringLiquibase liquibase(DataSource dataSource)
        throws Exception
    {
        if (StringUtils.isEmpty(module) || module.equals("''"))
        {
            throw new Exception("请配置--update.module=oms/wms/admin(只可配置一个),选择需要更新的数据库");
        }

        SpringLiquibase liquibase = new SpringLiquibase();
        liquibase.setDataSource(dataSource);
        liquibase.setChangeLog("classpath:" + module + "/master.xml");
        liquibase.setContexts("development,test,production");
        liquibase.setShouldRun(true);
        liquibase.setDatabaseChangeLogTable("ds_changelog");
        liquibase.setDatabaseChangeLogLockTable("ds_changelog_lock");
//        liquibase.setDefaultSchema(null);
        if (!StringUtils.isEmpty(liquibaseSchema))
        {
            liquibase.setLiquibaseSchema(liquibaseSchema);
        }

        return liquibase;
    }

}
