<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.db.mapper.SysDatabaseConfigMapper">

    <resultMap type="org.dromara.db.domain.SysDatabaseConfig" id="SysDatabaseConfigResult">
        <result property="dbId" column="db_id"/>
        <result property="name" column="name"/>
        <result property="url" column="url"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
</mapper>
