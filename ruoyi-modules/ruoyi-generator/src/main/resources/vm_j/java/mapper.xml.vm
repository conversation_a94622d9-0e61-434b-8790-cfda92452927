    ##引入mybatis支持
        $!{mybatisSupport.vm}

        ##设置保存名称与保存位置
        $!callback.setFileName($tool.append($!{tableInfo.name}, "Mapper.xml"))
        $!callback.setSavePath($tool.append($modulePath, "/src/main/resources/mapper"))

        ##拿到主键
        #if(!$tableInfo.pkColumn.isEmpty())
            #set($pk = $tableInfo.pkColumn.get(0))
        #end

    <?xml version="1.0" encoding="UTF-8"?>
    <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    <mapper namespace="$!{tableInfo.savePackageName}.mapper.$!{tableInfo.name}Mapper">

        <resultMap type="$!{tableInfo.savePackageName}.domain.pojo.$!{tableInfo.name}" id="$!{tableInfo.name}Map">
            #foreach($column in $tableInfo.fullColumn)
                <result property="$!column.name" column="$!column.obj.name" jdbcType="$!column.ext.jdbcType"/>
            #end
        </resultMap>

        <!--查询指定行数据-->
        <select id="select$!{tableInfo.name}List" resultMap="$!{tableInfo.name}Map">
            select
            #allSqlColumn()

            from $!tableInfo.obj.name
            <where>
                #foreach($column in $tableInfo.fullColumn)
                    <if test="$!column.name != null#if($column.type.equals("java.lang.String")) and $!column.name != ''#end">
                        and $!column.obj.name = #{$!column.name}
                    </if>
                #end
            </where>
        </select>
    </mapper>
