##导入宏定义
$!{define.vm}

##设置表后缀（宏定义）
#setTableSuffix("ServiceImpl")

##保存文件（宏定义）
#save("/service/impl", "ServiceImpl.java")

##包路径（宏定义）
#setPackageSuffix("service.impl")

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import $!{tableInfo.savePackageName}.mapper.$!{tableInfo.name}Mapper;
import $!{tableInfo.savePackageName}.domain.pojo.$!{tableInfo.name};
import $!{tableInfo.savePackageName}.domain.dto.$!{tableInfo.name}Dto;
import $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;
import org.springframework.stereotype.Service;
import $!{tableInfo.savePackageName}.mapper.$!{tableInfo.name}Mapper;

import java.util.List;
import lombok.RequiredArgsConstructor;

import com.ruoyi.common.utils.bean.BeanUtils;

##表注释（宏定义）
#tableComment("表服务实现类")
@Service("$!tool.firstLowerCase($tableInfo.name)Service")
@RequiredArgsConstructor
public class $!{tableName} extends ServiceImpl<$!{tableInfo.name}Dao, $!{tableInfo.name}> implements $!{tableInfo.name}Service {

    private final $!{tableInfo.name}Mapper $!tool.firstLowerCase($!{tableInfo.name})Mapper;
    /**
     *
     * @param  id 主键
     * <AUTHOR>
     * @description //TODO  通过ID查询单条数据
     * @date $!time.currTime()
     * @return 实例对象
     */
        @Override
        public $!{tableInfo.name} select$!{tableInfo.name}ById (Long id) {
        LambdaQueryWrapper<$!{tableInfo.name}> $!{tool.firstLowerCase($!{tableInfo.name})}lam =  new LambdaQueryWrapper<$!{tableInfo.name}>();
            $!{tool.firstLowerCase($!{tableInfo.name})}lam.eq($!{tableInfo.name} ::getId ,id);
        return baseMapper.selectOne($!{tool.firstLowerCase($!{tableInfo.name})}lam);
    }

    /**
     *
     * @param $!tool.firstLowerCase($!{tableInfo.name})dto 实例对象
     * <AUTHOR>
     * @description //TODO  分页查询数据
     * @date $!time.currTime()
     * @return 实例对象
     */
    @Override
    public List<$!{tableInfo.name}> find$!{tableInfo.name}SelectList ($!{tableInfo.name}Dto $!tool.firstLowerCase($!{tableInfo.name})dto) {
        $!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}) = new $!{tableInfo.name}();
        BeanUtils.copyBeanProp($!tool.firstLowerCase($!{tableInfo.name}),$!tool.firstLowerCase($!{tableInfo.name})dto);
        return this.$!{tool.firstLowerCase($tableInfo.name)}Mapper.select$!{tableInfo.name}List($!{tool.firstLowerCase($tableInfo.name)});
    }

    /**
     *
     * @param $!tool.firstLowerCase($!{tableInfo.name})dto 实例对象
     * <AUTHOR>
     * @description //TODO  新增数据
     * @date $!time.currTime()
     * @return 实例对象
     */
    @Override
    public int insert($!{tableInfo.name}Dto $!tool.firstLowerCase($!{tableInfo.name})dto) {
        $!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}) = new $!{tableInfo.name}();
        BeanUtils.copyBeanProp($!tool.firstLowerCase($!{tableInfo.name}),$!tool.firstLowerCase($!{tableInfo.name})dto);
        return baseMapper.insert($!tool.firstLowerCase($!{tableInfo.name}));
    }


    /**
     *
     * @param $!tool.firstLowerCase($!{tableInfo.name})dto 实例对象
     * <AUTHOR>
     * @description //TODO  修改数据并返回
     * @date $!time.currTime()
     * @return 实例对象
     */
    @Override
    public int update($!{tableInfo.name}Dto $!tool.firstLowerCase($!{tableInfo.name})dto) {
        $!{tableInfo.name} $!tool.firstLowerCase($!{tableInfo.name}) = new $!{tableInfo.name}();
        BeanUtils.copyBeanProp($!tool.firstLowerCase($!{tableInfo.name}),$!tool.firstLowerCase($!{tableInfo.name})dto);
        return  baseMapper.updateById($!{tool.firstLowerCase($!{tableInfo.name})});
    }


    /**
     *
     * @param idList 主键
     * <AUTHOR>
     * @description //TODO    通过主键删除数据
     * @date $!time.currTime()
     * @return
     */
    @Override
    public void deleteById(List<Long> idList) {
        return baseMapper.deleteBatchIds(idList);
    }
}
