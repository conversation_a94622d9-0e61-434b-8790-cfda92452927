##导入宏定义
$!{define.vm}

##设置表后缀（宏定义）
#setTableSuffix("Controller")

##保存文件（宏定义）
#save("/controller", "Controller.java")

##包路径（宏定义）
#setPackageSuffix("controller")

##定义服务名
#set($serviceName = $!tool.append($!tool.firstLowerCase($!tableInfo.name), "Service"))

##定义实体对象名
#set($entityName = $!tool.firstLowerCase($!tableInfo.name))

import $!{tableInfo.savePackageName}.domain.dto.$!{tableInfo.name}Dto;
import $!{tableInfo.savePackageName}.service.$!{tableInfo.name}Service;
import org.springframework.web.bind.annotation.*;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;

import java.util.List;
import lombok.RequiredArgsConstructor;

##表注释（宏定义）
#tableComment("表控制层")
@RestController
@RequestMapping("$!tool.firstLowerCase($!tableInfo.name)")
@RequiredArgsConstructor
public class $!{tableName} extends BaseController{
    /**
     * 服务对象
     */
    private final $!{tableInfo.name}Service $!{serviceName};

    /**
     *
     * @param $!{entityName}dto 请求参数封装
     * <AUTHOR>
     * @description //TODO  分页查询所有数据
     * @date $!time.currTime()
     * @return 实例对象
     */
    @PostMapping("/get$!{tableInfo.name}s")
    public TableDataInfo find$!{tableInfo.name}SelectList(@RequestBody $!{tableInfo.name}Dto $!{entityName}dto ) {
        startPage();
        return getDataTable(this.$!{serviceName}.find$!{tableInfo.name}SelectList($!{entityName}dto));
    }


    /**
     *
     * @param $!{entityName}dto 请求参数封装
     * <AUTHOR>
     * @description //TODO  通过主键查询单条数据
     * @date $!time.currTime()
     * @return 单条数据
     */
    @PostMapping("/get$!{tableInfo.name}ById")
    public AjaxResult select$!{tableInfo.name}ById(@RequestBody $!{tableInfo.name}Dto $!{entityName}dto) {
        return AjaxResult.success($!{serviceName}.select$!{tableInfo.name}ById($!{entityName}dto.getId()));
    }

    /**
     *
     * @param $!{entityName}dto 实体对象
     * <AUTHOR>
     * @description //TODO  新增数据
     * @date $!time.currTime()
     * @return 新增结果
     */
    @PostMapping("/add$!{tableInfo.name}")
    public AjaxResult add$!{tableInfo.name}(@RequestBody $!{tableInfo.name}Dto $!{entityName}dto) {
        return toAjax(this.$!{serviceName}.insert($!{entityName}dto));
    }

    /**
     *
     * @param $!{entityName}dto 实体对象
     * <AUTHOR>
     * @description //TODO  修改数据
     * @date $!time.currTime()
     * @return 修改结果
     */
    @PostMapping("/update$!{tableInfo.name}")
    public AjaxResult update(@RequestBody $!{tableInfo.name}Dto $!{entityName}dto) {
        return toAjax(this.$!{serviceName}.update($!{entityName}dto));
    }

    /**
     *
     * @param ids 主键集合
     * <AUTHOR>
     * @description //TODO  删除数据
     * @date $!time.currTime()
     * @return 删除结果
     */
    @PostMapping("/delete$!{tableInfo.name}")
    public AjaxResult delete(@RequestParam("ids") List<Long> ids) {
        return toAjax(this.$!{serviceName}.deleteById(ids));
    }
}
