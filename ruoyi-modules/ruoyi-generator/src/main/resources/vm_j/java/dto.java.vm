##引入宏定义
$!{define.vm}

##使用宏定义设置回调（保存位置与文件后缀）
#save("/domain/dto", ".java")

##使用宏定义设置包后缀
#setPackageSuffix("domain.dto")

##使用全局变量实现默认包导入
$!{autoImport.vm}
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

##使用宏定义实现类注释信息
#tableComment("实体类")
@Data
public class $!{tableInfo.name}Dto {

    #foreach($column in $tableInfo.fullColumn)
        #if(${column.comment})/**
         * ${column.comment}
         */#end

        private $!{tool.getClsNameByFullName($column.type)} $!{column.name};
    #end

    List<Long> ids;
    /**
    * 页
    */
    private Integer pageNum;
    /**
    * 条
    */
    private Integer pageSize;
    /**
    * 开始时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
    * 结束时间
    */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    public Integer getPageNum() {
        return pageNum == null ? 1 : pageNum;
    }

    public Integer getPageSize() {
        return pageSize == null ? 10 : pageSize;
    }
}
