##导入宏定义
$!{define.vm}

##设置表后缀（宏定义）
#setTableSuffix("Service")

##保存文件（宏定义）
#save("/service", "Service.java")

##包路径（宏定义）
#setPackageSuffix("service")

import com.baomidou.mybatisplus.extension.service.IService;
import $!{tableInfo.savePackageName}.domain.dto.$!tableInfo.nameDto;
import $!{tableInfo.savePackageName}.domain.pojo.$!tableInfo.name;
import java.util.List;

##表注释（宏定义）
#tableComment("表服务接口")
public interface $!{tableName} extends IService<$!tableInfo.name> {

    /**
     *
     * @param id 主键
     * <AUTHOR>
     * @description  通过ID查询单条数据
     * @date $!time.currTime()
     * @return 实例对象
     */
    public $!{tableInfo.name} select$!{tableInfo.name}ById (Long id);

    /**
     *
     * @param $!tool.firstLowerCase($!{tableInfo.name})dto 实例对象
     * <AUTHOR>
     * @description 分页查询数据
     * @date $!time.currTime()
     * @return 实例对象
     */
    public List<$!{tableInfo.name}> find$!{tableInfo.name}SelectList ($!{tableInfo.name}Dto $!tool.firstLowerCase($!{tableInfo.name})dto);


    /**
     *
     * @param $!tool.firstLowerCase($!{tableInfo.name})dto 实例对象
     * <AUTHOR>
     * @description  新增数据
     * @date $!time.currTime()
     * @return 实例对象
     */
    public int insert($!{tableInfo.name}Dto $!tool.firstLowerCase($!{tableInfo.name})dto);

    /**
     *
     * @param $!tool.firstLowerCase($!{tableInfo.name})dto 实例对象
     * <AUTHOR>
     * @description 修改数据并返回
     * @date $!time.currTime()
     * @return 实例对象
     */
    public int update($!{tableInfo.name}Dto $!tool.firstLowerCase($!{tableInfo.name})dto);


    /**
     *
     * @param ids 主键
     * <AUTHOR>
     * @description  通过主键删除数据
     * @date $!time.currTime()
     * @return
     */
    public int deleteById(List<Long> ids);
}
