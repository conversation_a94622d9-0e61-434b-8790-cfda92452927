package org.dromara.syeran.demo.tools.service;


import org.dromara.syeran.demo.dto.WeatherRequest;
import org.dromara.syeran.demo.dto.WeatherResponse;
import org.dromara.syeran.enums.Unit;

import java.util.function.Function;

public class WeatherService implements Function<WeatherRequest, WeatherResponse> {

    public WeatherResponse apply(WeatherRequest request) {
        System.out.println("获取当前位置的天气");
        return new WeatherResponse(33.0, Unit.C);
    }




}

