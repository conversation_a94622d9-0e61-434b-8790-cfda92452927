package org.dromara.syeran.service.chat;

import jakarta.servlet.http.HttpServletRequest;
import org.dromara.chat.request.ChatRequest;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


/**
 * 用户聊天管理Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISseService {

    /**
     * 客户端发送消息到服务端
     * @param chatRequest 请求对象
     */
    SseEmitter sseChat(ChatRequest chatRequest, HttpServletRequest request);

//    /**
//     * 语音转文字
//     * @param file 语音文件
//     */
//   WhisperResponse speechToTextTranscriptionsV2(MultipartFile file);
//
//    /**
//     * 文字转语音
//     *
//     * @param textToSpeech 文本信息
//     * @return 流式语音
//     */
//    ResponseEntity<Resource> textToSpeed(TextToSpeech textToSpeech);
//
//    /**
//     * 上传文件到服务器
//     *
//     * @param file 文件信息
//     * @return 返回文件信息
//     */
//    UploadFileResponse upload(MultipartFile file);


}
