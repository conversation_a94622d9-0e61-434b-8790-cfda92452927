package org.dromara.syeran.service.chat.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.chat.request.ChatRequest;
import org.dromara.syeran.enums.ChatModeType;
import org.dromara.syeran.service.chat.IChatService;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * openai
 * <AUTHOR>
 */
@Service
@Slf4j
public class OpenAIServiceImpl implements IChatService {

//    private final ChatClient chatClient;
//
//    public OpenAIServiceImpl(ChatClient.Builder chatClientBuilder, List<McpSyncClient> mcpSyncClients) {
//        this.chatClient = chatClientBuilder
//            .defaultOptions(OpenAiChatOptions.builder().model("gpt-4o-mini").build())
//            .defaultTools(new SyncMcpToolCallbackProvider(mcpSyncClients))
//            .build();
//    }
    @Override
    public String getCategory() {
        return ChatModeType.CHAT.getCode();
    }

    @Override
    public SseEmitter chat(ChatRequest chatRequest, SseEmitter emitter) {
//        chatClient.prompt().
        return null;
    }
}
