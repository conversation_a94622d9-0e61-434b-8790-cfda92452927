package org.dromara.syeran.controller.chat;


import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.chat.request.ChatRequest;
import org.dromara.syeran.service.chat.ISseService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;


/**
 *  聊天管理
 *
 * <AUTHOR>
 * @date 2023-03-01
 */
@Controller
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/chat")
public class ChatController {

    private final ISseService sseService;

    /**
     * 聊天接口
     */
    @PostMapping("/send")
    @ResponseBody
    public SseEmitter sseChat(@RequestBody @Valid ChatRequest chatRequest, HttpServletRequest request) {
        return sseService.sseChat(chatRequest,request);
    }

//    /**
//     * 上传文件
//     */
//    @PostMapping("/upload")
//    @ResponseBody
//    public UploadFileResponse upload(@RequestPart("file") MultipartFile file) {
//        return sseService.upload(file);
//    }
//
//
//    /**
//     * 语音转文本
//     *
//     * @param file
//     */
//    @PostMapping("/audio")
//    @ResponseBody
//    public WhisperResponse audio(@RequestParam("file") MultipartFile file) {
//        return sseService.speechToTextTranscriptionsV2(file);
//    }
//
//    /**
//     * 文本转语音
//     *
//     * @param textToSpeech
//     */
//    @PostMapping("/speech")
//    @ResponseBody
//    public ResponseEntity<Resource> speech(@RequestBody TextToSpeech textToSpeech) {
//        return sseService.textToSpeed(textToSpeech);
//    }

}
