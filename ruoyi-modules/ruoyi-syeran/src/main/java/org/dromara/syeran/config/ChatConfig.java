package org.dromara.syeran.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;

//@Configuration
public class ChatConfig {

    /**
     * 配置 ChatClient
     * @param zhiPuAiChatModel
     * @param tools
     * @return
     */
    @Bean
    public ChatClient zhiPuAiChatClient(@Qualifier("zhiPuAiChatModel") ChatModel zhiPuAiChatModel, ToolCallbackProvider tools) {
        return ChatClient.builder(zhiPuAiChatModel)
                .defaultToolCallbacks(tools)
                .build();
    }


}
