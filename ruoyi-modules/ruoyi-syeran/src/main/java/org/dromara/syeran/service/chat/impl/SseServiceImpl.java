package org.dromara.syeran.service.chat.impl;

import cn.hutool.core.collection.CollectionUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.chat.entity.chat.Message;
import org.dromara.chat.request.ChatRequest;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.domain.bo.QueryVectorBo;
import org.dromara.domain.vo.ChatModelVo;
import org.dromara.domain.vo.KnowledgeInfoVo;
import org.dromara.service.IChatModelService;
import org.dromara.service.IKnowledgeInfoService;
import org.dromara.service.VectorStoreService;
import org.dromara.syeran.factory.ChatServiceFactory;
import org.dromara.syeran.service.chat.IChatService;
import org.dromara.syeran.service.chat.ISseService;
import org.dromara.syeran.utils.SSEUtil;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SseServiceImpl implements ISseService {

    private final IChatModelService chatModelService;

    private final IKnowledgeInfoService knowledgeInfoService;

    private final VectorStoreService vectorStoreService;

    private final ChatServiceFactory chatServiceFactory;

    private ChatModelVo chatModelVo;

    @Override
    public SseEmitter sseChat(ChatRequest chatRequest, HttpServletRequest request) {
        SseEmitter sseEmitter = new SseEmitter(0L);
        try {
            // 构建消息列表
            buildChatMessageList(chatRequest);
            // 设置对话角色
            chatRequest.setRole(Message.Role.USER.getName());

            if (LoginHelper.isLogin()) {
                // 保存消息记录 并扣除费用
//                chatCostService.deductToken(chatRequest);
//                chatRequest.setUserId(chatCostService.getUserId());
//                if (chatRequest.getSessionId() == null) {
//                    ChatSessionBo chatSessionBo = new ChatSessionBo();
//                    chatSessionBo.setUserId(chatCostService.getUserId());
//                    chatSessionBo.setSessionTitle(getFirst10Characters(chatRequest.getPrompt()));
//                    chatSessionBo.setSessionContent(chatRequest.getPrompt());
//                    chatSessionService.insertByBo(chatSessionBo);
//                    chatRequest.setSessionId(chatSessionBo.getId());
//                }
            }
            // 根据模型分类调用不同的处理逻辑
            IChatService chatService = chatServiceFactory.getChatService(chatModelVo.getCategory());
            chatService.chat(chatRequest, sseEmitter);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            SSEUtil.sendErrorEvent(sseEmitter, e.getMessage());
        }
        return sseEmitter;
    }


    /**
     * 构建消息列表
     */
    private void buildChatMessageList(ChatRequest chatRequest) {
        String sysPrompt;
        // 矫正模型名称 如果是gpt-image 则查询image类型模型 获取模型名称
        if (chatRequest.getModel().equals("gpt-image")) {
            chatModelVo = chatModelService.selectModelByCategory("image");
            if (chatModelVo == null) {
                log.error("未找到image类型的模型配置");
                throw new IllegalStateException("未找到image类型的模型配置");
            }
        } else {
            chatModelVo = chatModelService.selectModelByName(chatRequest.getModel());
        }
        // 获取对话消息列表
        List<Message> messages = chatRequest.getMessages();
        // 查询向量库相关信息加入到上下文
        if (StringUtils.isNotEmpty(chatRequest.getKid())) {
            List<Message> knMessages = new ArrayList<>();
            String content = messages.get(messages.size() - 1).getContent().toString();
            // 通过kid查询知识库信息
            KnowledgeInfoVo knowledgeInfoVo = knowledgeInfoService.queryById(Long.valueOf(chatRequest.getKid()));
            // 查询向量模型配置信息
            ChatModelVo chatModel = chatModelService.selectModelByName(knowledgeInfoVo.getEmbeddingModelName());

            QueryVectorBo queryVectorBo = new QueryVectorBo();
            queryVectorBo.setQuery(content);
            queryVectorBo.setKid(chatRequest.getKid());
            queryVectorBo.setApiKey(chatModel.getApiKey());
            queryVectorBo.setBaseUrl(chatModel.getApiHost());
            queryVectorBo.setVectorModelName(knowledgeInfoVo.getVectorModelName());
            queryVectorBo.setEmbeddingModelName(knowledgeInfoVo.getEmbeddingModelName());
            queryVectorBo.setMaxResults(knowledgeInfoVo.getRetrieveLimit());
            List<String> nearestList = vectorStoreService.getQueryVector(queryVectorBo);
            for (String prompt : nearestList) {
                Message userMessage = Message.builder().content(prompt).role(Message.Role.USER).build();
                knMessages.add(userMessage);
            }
            messages.addAll(knMessages);
            // 设置知识库系统提示词
            sysPrompt = knowledgeInfoVo.getSystemPrompt();
            if (StringUtils.isEmpty(sysPrompt)) {
                sysPrompt = "###角色设定\n" +
                    "你是一个智能知识助手，专注于利用上下文中的信息来提供准确和相关的回答。\n" +
                    "###指令\n" +
                    "当用户的问题与上下文知识匹配时，利用上下文信息进行回答。如果问题与上下文不匹配，运用自身的推理能力生成合适的回答。\n" +
                    "###限制\n" +
                    "确保回答清晰简洁，避免提供不必要的细节。始终保持语气友好" +
                    "当前时间：" + DateUtils.getDate();
            }
        } else {
            sysPrompt = chatModelVo.getSystemPrompt();
            if (StringUtils.isEmpty(sysPrompt)) {
                sysPrompt = "你是一个由RuoYI-AI开发的人工智能助手，名字叫熊猫助手。你擅长中英文对话，能够理解并处理各种问题，提供安全、有帮助、准确的回答。" +
                    "当前时间：" + DateUtils.getDate() +
                    "#注意：回复之前注意结合上下文和工具返回内容进行回复。";
            }
        }
        // 设置系统默认提示词
        Message sysMessage = Message.builder().content(sysPrompt).role(Message.Role.SYSTEM).build();
        messages.add(0, sysMessage);

        chatRequest.setSysPrompt(sysPrompt);
        // 用户对话内容
        String chatString = null;
        // 获取用户对话信息
        Object content = messages.get(messages.size() - 1).getContent();
        if (content instanceof List<?> listContent) {
            if (CollectionUtil.isNotEmpty(listContent)) {
                chatString = listContent.get(0).toString();
            }
        } else if (content instanceof String) {
            chatString = (String) content;
        }
        // 设置对话信息
        chatRequest.setPrompt(chatString);
    }

}
