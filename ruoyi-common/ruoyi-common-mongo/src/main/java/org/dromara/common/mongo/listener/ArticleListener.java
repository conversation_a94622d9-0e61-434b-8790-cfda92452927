package org.dromara.common.mongo.listener;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mongo.service.IArticleService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.Ordered;

@Slf4j
//@Component
@RequiredArgsConstructor
public class ArticleListener implements ApplicationRunner, Ordered {

    private final IArticleService articleService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("初始化成功订阅文章查询监听器");
//        //订阅文章查询，并推送到前端
//        ArticleUtils.subscribeMessage(articleMessageDTO -> {
//            List<Long> sessionId = articleMessageDTO.getSessionId();
//            String articleId = articleMessageDTO.getArticleId();
//            SyArticle syArticle = articleService.selectById(articleId);
//            if (syArticle == null) {
//                return;
//            }
//            for (Long id : sessionId) {
//                ArticleUtils.sendMessage(id, JsonUtils.toJsonString(syArticle));
//            }
//        });
    }

    @Override
    public int getOrder() {
        return -1;
    }
}
