package org.dromara.common.mongo.core.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 文章评论实体类
 */
@Document(collection = "sy_article")
@Data
@EqualsAndHashCode(callSuper = true)
public class SyArticle extends BaseCollection {

    @Id
    private String articleId;
    /**
     * 笔记内容
     */
    private String content;

    @Indexed
    private String userid;

}
