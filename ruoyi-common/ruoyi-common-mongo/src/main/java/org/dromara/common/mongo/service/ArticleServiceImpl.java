package org.dromara.common.mongo.service;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.dromara.common.mongo.core.domain.SyArticle;
import org.dromara.common.mongo.repository.ArticleRepository;

//@Service
@RequiredArgsConstructor
public class ArticleServiceImpl implements IArticleService {

    private final ArticleRepository articleRepository;

    @Override
    public SyArticle selectById(String articleId) {
        return articleRepository.findById(articleId).orElse(null);
    }

    @Override
    public void saveArticle(SyArticle syArticle) {
        SyArticle save = articleRepository.save(syArticle);
        if(ObjectUtil.isEmpty(save)){
            throw new RuntimeException("文章保存失败");
        }
    }
}
