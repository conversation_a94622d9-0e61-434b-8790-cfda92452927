package org.dromara.common.mongo.core.domain;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 文章评论实体类
 */
@Document(collection = "comment")
@Data
public class SyComment implements Serializable {
    @Id
    private String id;

    @Field("content")
    private String content;

    private LocalDateTime publishTime;

    @Indexed
    private String userid;

    private String nickname;

    private LocalDateTime createTime;

    private Integer likeNum;

    private Integer replyNum;

    private String state;

    private String parentId;

    private String articleId;

}
