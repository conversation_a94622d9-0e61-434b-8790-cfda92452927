package org.dromara.common.mongo.service;

import org.springframework.stereotype.Service;

@Service
public class AIServiceImpl implements IAIService {


    @Override
    public String sparkAi(String prompt) {
        String spark = spark(prompt);
        return spark;
    }


    void openAi() {

//        OpenAiLlmConfig config = new OpenAiLlmConfig();
//        config.setApiKey("sk-rts5NF6n*******");
//
//        Llm llm = new OpenAiLlm(config);
//        String response = llm.chat("请问你叫什么名字");
//
//        System.out.println(response);
    }

    private String spark(String prompt) {
//        SparkLlmConfig config = new SparkLlmConfig();
//        config.setAppId("eb166a77");
//        config.setApiKey("348f5ca59efaba0e4606025d1ba5efc1");
//        config.setApiSecret("ZjVjOTM3YWU0MjI5NjIzM2M0ZTZjNDA1");
//
//        Llm llm = new SparkLlm(config);
//        return llm.chat(prompt);
        return null;
    }
}
