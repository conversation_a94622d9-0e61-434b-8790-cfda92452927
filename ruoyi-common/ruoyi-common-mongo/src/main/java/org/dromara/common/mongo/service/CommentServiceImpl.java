package org.dromara.common.mongo.service;

import lombok.RequiredArgsConstructor;
import org.dromara.common.mongo.core.domain.SyComment;
import org.dromara.common.mongo.repository.CommentRepository;

import java.util.List;
import java.util.Optional;

//@Service
@RequiredArgsConstructor
public class CommentServiceImpl implements ICommentService {

    private final CommentRepository commentRepository;

    /**
     * 保存一个评论
     * @param syComment
     */
    public void saveComment(SyComment syComment){
        commentRepository.save(syComment);
    }

    /**
     * 更新评论
     * @param syComment
     */
    public void updateComment(SyComment syComment){
        commentRepository.save(syComment);
    }
    /**
     * 根据id删除评论
     * @param id
     */
    public void deleteCommentById(String id){
        commentRepository.deleteById(id);
    }
    /**
     * 查询所有评论
     * @return
     */
    public List<SyComment> findCommentList(){
        return commentRepository.findAll();
    }
    /**
     * 根据id查询评论
     * @param id
     * @return
     */
    public SyComment findCommentById(String id){
        Optional<SyComment> byId = commentRepository.findById(id);
        return byId.orElse(null);
    }
}
