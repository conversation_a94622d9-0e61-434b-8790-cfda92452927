package org.dromara.common.mongo.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mongo.service.IAIService;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文档管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/syeran/ai")
public class NoteController extends BaseController {

    private final IAIService iaiService;

    /**
     * 根据ID查询文档
     */
    @GetMapping("/select/by/{id}")
    public R<String> selectById(@PathVariable String id) {

        return R.ok();
    }


}
