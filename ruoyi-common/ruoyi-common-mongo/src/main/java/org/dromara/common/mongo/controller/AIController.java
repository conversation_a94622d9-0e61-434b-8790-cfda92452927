package org.dromara.common.mongo.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.mongo.service.IAIService;
import org.dromara.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对接 AI 对话大模型
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/syeran/ai")
public class AIController extends BaseController {

    private final IAIService iaiService;

    /**
     * 讯飞星火
     */
    @GetMapping("/spark")
    public R<String> spark(String prompt) {
        String resp = iaiService.sparkAi(prompt);
        return R.ok(resp);
    }


}
