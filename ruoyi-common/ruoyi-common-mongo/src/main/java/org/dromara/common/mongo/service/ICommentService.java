package org.dromara.common.mongo.service;

import org.dromara.common.mongo.core.domain.SyComment;

import java.util.List;

public interface ICommentService {


    /**
     * 保存一个评论
     *
     * @param syComment
     */
    void saveComment(SyComment syComment);

    /**
     * 更新评论
     *
     * @param syComment
     */
    void updateComment(SyComment syComment);

    /**
     * 根据id删除评论
     *
     * @param id
     */
    void deleteCommentById(String id);

    /**
     * 查询所有评论
     *
     * @return
     */
    List<SyComment> findCommentList();

    /**
     * 根据id查询评论
     *
     * @param id
     * @return
     */
    SyComment findCommentById(String id);

}
