package org.dromara.common.oss.core;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.common.oss.exception.OssException;
import org.dromara.common.oss.properties.OssProperties;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.function.Consumer;

/**
 * 本地文件上传下载
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public class LocalOssClient extends OssClient{
    /**
     * 配置属性
     */
    private final OssProperties properties;
    /**
     * 本地存储根路径
     */
    private final String basePath;
    /**
     * 构造方法
     *
     * @param configKey     配置键
     * @param ossProperties Oss配置属性
     */
    public LocalOssClient(String configKey, OssProperties ossProperties) {
        super(configKey, ossProperties);
        this.properties = ossProperties;
        // 设置本地存储的基础路径，可以从配置中获取或使用默认值
        this.basePath = properties.getBucketName();
        // 确保基础路径存在
        FileUtil.mkdir(basePath);
    }


    /**
     * 上传文件到本地
     *
     * @param file   要上传的文件
     * @param suffix 对象键的后缀
     * @return UploadResult 包含上传后的文件信息
     */
    @Override
    public UploadResult uploadSuffix(File file, String suffix) {
        try {
            String fileName = getPath(properties.getPrefix(), suffix);
            String filePath = basePath + StringUtils.SLASH + fileName;
            FileUtil.copy(file, new File(filePath), true);

            return UploadResult.builder()
                .url(getLocalUrl() + StringUtils.SLASH + fileName)
                .filename(fileName)
                .eTag("") // 本地文件可以不设置ETag
                .build();
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败:[" + e.getMessage() + "]");
        }
    }

    /**
     * 上传 InputStream 到本地
     *
     * @param inputStream 要上传的输入流
     * @param suffix      文件后缀
     * @return UploadResult 包含上传后的文件信息
     */
    public UploadResult upload(InputStream inputStream, String suffix) {
        try {
            String fileName = getPath(properties.getPrefix(), suffix);
            String filePath = basePath + StringUtils.SLASH + fileName;
            FileUtil.writeFromStream(inputStream, new File(filePath));

            return UploadResult.builder()
                .url(getLocalUrl() + StringUtils.SLASH + fileName)
                .filename(fileName)
                .eTag("")
                .build();
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败:[" + e.getMessage() + "]");
        } finally {
            IoUtil.close(inputStream);
        }
    }

    /**
     * 上传 byte[] 数据到 Amazon S3，使用指定的后缀构造对象键。
     *
     * @param data   要上传的 byte[] 数据
     * @param suffix 对象键的后缀
     * @return UploadResult 包含上传后的文件信息
     * @throws OssException 如果上传失败，抛出自定义异常
     */
    @Override
    public UploadResult uploadSuffix(byte[] data, String suffix, String contentType) {
        try {
            String fileName = getPath(properties.getPrefix(), suffix);
            String filePath = basePath + StringUtils.SLASH + fileName;
            FileUtil.writeBytes(data, new File(filePath));

            return UploadResult.builder()
                .url(getLocalUrl() + StringUtils.SLASH + fileName)
                .filename(fileName)
                .eTag("")
                .build();
        } catch (Exception e) {
            throw new RuntimeException("上传文件失败:[" + e.getMessage() + "]");
        }
    }

    /**
     * 下载本地文件到输出流
     *
     * @param filePath 文件路径
     * @param out      输出流
     * @param consumer 自定义处理逻辑
     */
    public void download(String filePath, OutputStream out, Consumer<Long> consumer) {
        FileInputStream fis = null;
        try {
            File file = new File(basePath + StringUtils.SLASH + removeBaseUrl(filePath));
            if (!file.exists()) {
                throw new RuntimeException("文件不存在:" + filePath);
            }

            fis = new FileInputStream(file);
            if (consumer != null) {
                consumer.accept(file.length());
            }
            IoUtil.copy(fis, out);
        } catch (Exception e) {
            throw new RuntimeException("下载文件失败:[" + e.getMessage() + "]");
        } finally {
            IoUtil.close(fis);
        }
    }

    /**
     * 获取文件输入流
     *
     * @param filePath 文件路径
     * @return 输入流
     */
    public InputStream getObjectContent(String filePath) throws IOException {
        return Files.newInputStream(Paths.get(basePath, removeBaseUrl(filePath)));
    }

    /**
     * 删除本地文件
     *
     * @param filePath 文件路径
     */
    public void delete(String filePath) {
        try {
            FileUtil.del(new File(basePath + StringUtils.SLASH + removeBaseUrl(filePath)));
        } catch (Exception e) {
            throw new RuntimeException("删除文件失败:[" + e.getMessage() + "]");
        }
    }

    /**
     * 获取本地文件访问URL
     *
     * @return 文件访问URL
     */
    public String getLocalUrl() {
        String endpoint = properties.getEndpoint();
        String header = getIsHttps();
        return header + endpoint;
    }

    /**
     * 移除路径中的基础URL部分，得到相对路径
     *
     * @param path 完整的路径
     * @return 相对路径
     */
    public String removeBaseUrl(String path) {
        return path.replace(getLocalUrl() + StringUtils.SLASH, "");
    }

    /**
     * 获取私有URL链接（本地文件不适用，仅作兼容）
     *
     * @param filePath    文件路径
     * @param expiredTime 链接授权到期时间
     */
    public String getPrivateUrl(String filePath, Duration expiredTime) {
        // 本地文件直接返回文件路径
        return "file://" + basePath + StringUtils.SLASH + removeBaseUrl(filePath);
    }
}
