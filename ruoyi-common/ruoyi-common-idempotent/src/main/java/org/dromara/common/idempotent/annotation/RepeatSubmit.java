package org.dromara.common.idempotent.annotation;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * 自定义注解防止表单重复提交
 *
 * <AUTHOR> Li
 */
@Inherited //作用：@RepeatSubmit注解可以被子类继承。
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented //作用：会包含在 Javadoc 中，方便开发者理解
public @interface RepeatSubmit {

    /**
     * 间隔时间(ms)，小于此时间视为重复提交
     */
    int interval() default 5000;

    TimeUnit timeUnit() default TimeUnit.MILLISECONDS;

    /**
     * 提示消息 支持国际化 格式为 {code}
     */
    String message() default "{repeat.submit.message}";

}
