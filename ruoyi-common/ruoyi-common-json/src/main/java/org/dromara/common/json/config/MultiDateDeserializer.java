package org.dromara.common.json.config;


import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 日期序列化多种格式转换
 */
//public class MultiDateDeserializer extends JsonDeserializer<Date> {
//
//    @Override
//    public Date deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
//        String dateStr = p.getText();
//        System.out.println("》》》》》》》》》》》》》》》》》》》  Parsing formatter: " + dateStr + "  >>>>>>>>>>>>>>>>>>>>>>"); // 添加日志
//
//        // 如果是 yyyy-MM 格式，补全为当月第一天
//        if (dateStr.length() == 7) {
//            LocalDate localDate = LocalDate.parse(dateStr + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//            return Date.valueOf(localDate);
//        }
//
//        LocalDate localDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        return Date.valueOf(localDate);
//    }
//}
@Component
public class MultiDateDeserializer implements Converter<String, LocalDate> {
    @Override
    public LocalDate convert(String source) {
        try {
            if (source.length() == 7) { // 处理 yyyy-MM 格式
                return LocalDate.parse(source + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }
            // 处理 yyyy-MM-dd 格式
            return LocalDate.parse(source, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } catch (Exception e) {
            throw new IllegalArgumentException("无效的日期格式: " + source);
        }
    }
}
