package org.dromara.ai.config;

import org.dromara.common.core.factory.YmlPropertySourceFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.PropertySource;

@AutoConfiguration
//@ConditionalOnProperty(value = "websocket.enabled", havingValue = "true")
//@EnableConfigurationProperties(WebSocketProperties.class)
@PropertySource(value = "classpath:spring-ai.yml", factory = YmlPropertySourceFactory.class)
public class SpringAiConfig implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        System.out.println(">>>>>> spring ai config.");
    }
}
