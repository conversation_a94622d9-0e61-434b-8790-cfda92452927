<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.dromara</groupId>
        <artifactId>ruoyi-common</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ruoyi-common-ai</artifactId>

    <description>
        ruoyi-common-ai spring-ai 应用模块
    </description>

    <properties>
        <dl4j-master.version>1.0.0-beta7</dl4j-master.version>
        <nd4j.backend>nd4j-native</nd4j.backend>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>ruoyi-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-mcp-client</artifactId>
        </dependency>

        <!-- Model -->
        <!-- openai 文档：https://docs.spring.io/spring-ai/reference/api/chat/openai-chat.html-->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-zhipuai</artifactId>
        </dependency>

        <!--store-->
        <!--        <dependency>-->
        <!--            <groupId>org.springframework.ai</groupId>-->
        <!--            <artifactId>spring-ai-starter-vector-store-milvus</artifactId>-->
        <!--        </dependency>-->
        <!--手动配置 MilvusVectorStore -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-milvus-store</artifactId>
        </dependency>

        <!--RAG 检索增强生成 -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-advisors-vector-store</artifactId>
        </dependency>


        <!--        <dependency>-->
<!--            <groupId>com.agentsflex</groupId>-->
<!--            <artifactId>agents-flex-bom</artifactId>-->
<!--        </dependency>-->


        <!--deepleaning4j-->
        <!--        <dependency>-->
        <!--            <groupId>org.deeplearning4j</groupId>-->
        <!--            <artifactId>deeplearning4j-core</artifactId>-->
        <!--            <version>${dl4j-master.version}</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.nd4j</groupId>-->
        <!--            <artifactId>${nd4j.backend}</artifactId>-->
        <!--            <version>${dl4j-master.version}</version>-->
        <!--        </dependency>-->

        <!--用于本地GPU-->
        <!--        <dependency>-->
        <!--            <groupId>org.deeplearning4j</groupId>-->
        <!--            <artifactId>deeplearning4j-cuda-9.2</artifactId>-->
        <!--            <version>${dl4j-master.version}</version>-->
        <!--        </dependency>-->

        <!--        <dependency>-->
        <!--            <groupId>org.nd4j</groupId>-->
        <!--            <artifactId>nd4j-cuda-9.2-platform</artifactId>-->
        <!--            <version>${dl4j-master.version}</version>-->
        <!--        </dependency>-->

    </dependencies>
</project>
