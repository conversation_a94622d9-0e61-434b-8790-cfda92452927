package org.dromara.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.domain.ChatModel;
import org.dromara.domain.vo.ChatModelVo;
import org.dromara.mapper.ChatModelMapper;
import org.dromara.service.IChatModelService;
import org.springframework.stereotype.Service;

/**
 * 聊天模型Service业务层处理
 */
@RequiredArgsConstructor
@Service
public class ChatModelServiceImpl implements IChatModelService {

    private final ChatModelMapper baseMapper;

    /**
     * 通过模型名称获取模型信息
     */
    @Override
    public ChatModelVo selectModelByName(String modelName) {
        return baseMapper.selectVoOne(Wrappers.<ChatModel>lambdaQuery().eq(ChatModel::getModelName, modelName));
    }

    /**
     * 通过模型分类获取模型信息
     */
    @Override
    public ChatModelVo selectModelByCategory(String  category) {
        return baseMapper.selectVoOne(Wrappers.<ChatModel>lambdaQuery().eq(ChatModel::getCategory, category));
    }
}
