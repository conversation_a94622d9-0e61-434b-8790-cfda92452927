package org.dromara.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.domain.bo.QueryVectorBo;
import org.dromara.service.VectorStoreService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 向量库管理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class VectorStoreServiceImpl implements VectorStoreService {

    @Override
    public List<String> getQueryVector(QueryVectorBo queryVectorBo) {
        return List.of();
    }
}
