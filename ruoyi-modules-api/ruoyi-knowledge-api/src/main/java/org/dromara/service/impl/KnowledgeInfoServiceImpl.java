package org.dromara.service.impl;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.domain.vo.KnowledgeInfoVo;
import org.dromara.mapper.KnowledgeInfoMapper;
import org.dromara.service.IKnowledgeInfoService;
import org.springframework.stereotype.Service;

/**
 * 知识库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class KnowledgeInfoServiceImpl implements IKnowledgeInfoService {

    private final KnowledgeInfoMapper baseMapper;

    /**
     * 查询知识库
     */
    @Override
    public KnowledgeInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }
}
