package org.dromara.test.ai;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class AgentFlexTest {
//    static SparkLlmConfig config = new SparkLlmConfig();
//    static {
//        config.setAppId("eb166a77");
//        config.setApiKey("348f5ca59efaba0e4606025d1ba5efc1");
//        config.setApiSecret("ZjVjOTM3YWU0MjI5NjIzM2M0ZTZjNDA1");
//    }

    @Test
    public void testEmbedding() {
//        Llm llm = new SparkLlm(config);
//        VectorData embeddings = llm.embed(Document.of("some document text"));
//        System.out.println(Arrays.toString(embeddings.getVector()));
    }
    @Test
    public void testChatStream() {
//        Llm llm = new SparkLlm(config);
//        String prompt = "你是一个AI专家，介绍一下LLM和AI相关开发的知识。";
//         llm.chatStream(prompt,(context, response)->{
//             System.out.println(response.getMessage().getContent());
//         });
    }

    @Test
    public void testChat() {
//        Llm llm = new SparkLlm(config);
//        String prompt = "你是一个AI专家，介绍一下LLM和AI相关开发的知识。";
//        String chat = llm.chat(prompt);
//        System.out.println(chat);
    }

}
