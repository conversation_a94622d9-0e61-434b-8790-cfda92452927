//package org.dromara.test.comment;
//
//import org.junit.jupiter.api.Tag;
//import org.junit.jupiter.api.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.time.LocalDateTime;
//import java.util.List;
//
//@SpringBootTest
//public class SyCommentTest {
//
////    @Autowired
//    private ICommentService commentService;
//
////    @Autowired
//    private IArticleService articleService;
//    /**
//     * 保存一个评论
//     */
//    @Test
//    @Tag("dev")
//    public void testSaveArticle() {
//        SyArticle syArticle = new SyArticle();
////        syArticle.setArticleId("");
//        syArticle.setContent("测试文章内容");
//        syArticle.setUserid("1");
//        syArticle.setCreateTime(LocalDateTime.now().toString());
//        syArticle.setUpdateTime(LocalDateTime.now().toString());
//
//        articleService.saveArticle(syArticle);
//    }
//
//    /**
//     * 保存一个评论
//     */
//    @Test
//    @Tag("dev")
//    public void testSaveComment() {
//        SyComment syComment = new SyComment();
//        syComment.setArticleId("100000");
//        syComment.setContent("测试添加的数据");
//        syComment.setCreateTime(LocalDateTime.now());
//        syComment.setUserid("1003");
//        syComment.setNickname("凯撒大帝");
//        syComment.setState("1");
//        syComment.setLikeNum(0);
//        syComment.setReplyNum(0);
//        commentService.saveComment(syComment);
//    }
//
//    /**
//     * 查询所有数据
//     */
//    @Test
//    public void testFindAll() {
//        List<SyComment> list = commentService.findCommentList();
//        System.out.println(list);
//    }
//
//    /**
//     * 测试根据id查询
//     */
//    @Test
//    public void testFindCommentById() {
//        SyComment syComment = commentService.findCommentById("5d6a27b81b8d374798cf0b41");
//        System.out.println(syComment);
//    }
//}
