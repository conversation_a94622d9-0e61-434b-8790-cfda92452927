package org.dromara.test.util;

import org.dromara.common.redis.utils.SequenceUtils;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.Duration;

@SpringBootTest
public class TestDemo {

    @Test
    public void test001(){
        System.out.println(SequenceUtils.nextId("user:service:id", Duration.ofMinutes(86400)));
        System.out.println(SequenceUtils.nextIdDate("prefix001"));
        System.out.println(SequenceUtils.nextIdDateTime("prefix002"));
    }
}
